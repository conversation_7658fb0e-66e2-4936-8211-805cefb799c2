<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        textarea, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 沉淀应用 - 前端功能测试</h1>
    
    <div class="test-section">
        <h2>📝 测试1: 文本内容处理</h2>
        <textarea id="textInput" rows="4" placeholder="输入要处理的文本内容...">人工智能正在改变我们的世界。它不仅提高了工作效率，还为我们带来了全新的可能性。然而，我们也需要思考如何与AI和谐共处，确保技术为人类服务。</textarea>
        <button onclick="testTextProcessing()">处理文本</button>
        <div id="textResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>🌐 测试2: URL内容处理</h2>
        <input type="url" id="urlInput" placeholder="输入网页URL..." value="https://example.com">
        <button onclick="testUrlProcessing()">处理URL</button>
        <div id="urlResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>💬 测试3: AI聊天功能</h2>
        <input type="text" id="chatInput" placeholder="输入问题..." value="这个内容的主要观点是什么？">
        <button onclick="testChat()">发送消息</button>
        <div id="chatResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>📋 测试4: 知识库功能</h2>
        <button onclick="testArchiveList()">获取会话列表</button>
        <button onclick="testSaveSession()">保存测试会话</button>
        <div id="archiveResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>🔗 测试5: 页面导航</h2>
        <button onclick="openMainPage()">打开主页</button>
        <button onclick="openArchivePage()">打开知识库页面</button>
        <div class="info">点击按钮将在新标签页中打开对应页面</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        let lastProcessedContent = null;

        async function testTextProcessing() {
            const text = document.getElementById('textInput').value;
            const resultDiv = document.getElementById('textResult');
            
            if (!text.trim()) {
                showResult(resultDiv, '请输入文本内容', 'error');
                return;
            }
            
            showResult(resultDiv, '正在处理...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        input: text,
                        type: 'text'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    lastProcessedContent = data;
                    showResult(resultDiv, 
                        `✅ 处理成功！\n\n标题: ${data.title}\n内容长度: ${data.content?.length || 0} 字符\nAI笔记长度: ${data.aiNote?.length || 0} 字符\n\nAI笔记预览:\n${data.aiNote?.substring(0, 200)}...`, 
                        'success'
                    );
                } else {
                    showResult(resultDiv, `❌ 处理失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 错误: ${error.message}`, 'error');
            }
        }

        async function testUrlProcessing() {
            const url = document.getElementById('urlInput').value;
            const resultDiv = document.getElementById('urlResult');
            
            if (!url.trim()) {
                showResult(resultDiv, '请输入URL', 'error');
                return;
            }
            
            showResult(resultDiv, '正在处理...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        input: url,
                        type: 'url'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    lastProcessedContent = data;
                    showResult(resultDiv, 
                        `✅ 处理成功！\n\n标题: ${data.title}\n内容长度: ${data.content?.length || 0} 字符\nAI笔记长度: ${data.aiNote?.length || 0} 字符\n\nAI笔记预览:\n${data.aiNote?.substring(0, 200)}...`, 
                        'success'
                    );
                } else {
                    showResult(resultDiv, `❌ 处理失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 错误: ${error.message}`, 'error');
            }
        }

        async function testChat() {
            const message = document.getElementById('chatInput').value;
            const resultDiv = document.getElementById('chatResult');
            
            if (!message.trim()) {
                showResult(resultDiv, '请输入问题', 'error');
                return;
            }
            
            if (!lastProcessedContent) {
                showResult(resultDiv, '请先处理一些内容再进行聊天', 'error');
                return;
            }
            
            showResult(resultDiv, '正在思考...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        context: {
                            originalContent: lastProcessedContent.content,
                            aiNote: lastProcessedContent.aiNote
                        }
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(resultDiv, 
                        `✅ AI回复:\n\n${data.response}`, 
                        'success'
                    );
                } else {
                    showResult(resultDiv, `❌ 聊天失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 错误: ${error.message}`, 'error');
            }
        }

        async function testArchiveList() {
            const resultDiv = document.getElementById('archiveResult');
            showResult(resultDiv, '正在获取...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/archive/list`);
                
                if (response.ok) {
                    const data = await response.json();
                    const sessions = data.sessions || [];
                    showResult(resultDiv, 
                        `✅ 获取成功！\n\n总会话数: ${sessions.length}\n\n最近的会话:\n${sessions.slice(0, 3).map(s => `- ${s.title} (${new Date(s.createdAt).toLocaleString()})`).join('\n')}`, 
                        'success'
                    );
                } else {
                    showResult(resultDiv, `❌ 获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 错误: ${error.message}`, 'error');
            }
        }

        async function testSaveSession() {
            const resultDiv = document.getElementById('archiveResult');
            
            if (!lastProcessedContent) {
                showResult(resultDiv, '请先处理一些内容再保存', 'error');
                return;
            }
            
            showResult(resultDiv, '正在保存...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/archive/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: lastProcessedContent.title,
                        sourceType: 'text',
                        sourceData: '测试数据',
                        originalContent: lastProcessedContent.content,
                        aiNoteMarkdown: lastProcessedContent.aiNote
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(resultDiv, 
                        `✅ 保存成功！\n\n会话ID: ${data.id}`, 
                        'success'
                    );
                } else {
                    showResult(resultDiv, `❌ 保存失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 错误: ${error.message}`, 'error');
            }
        }

        function openMainPage() {
            window.open(`${API_BASE}/`, '_blank');
        }

        function openArchivePage() {
            window.open(`${API_BASE}/archive`, '_blank');
        }

        function showResult(element, message, type) {
            element.style.display = 'block';
            element.textContent = message;
            element.className = `result ${type}`;
        }
    </script>
</body>
</html>
