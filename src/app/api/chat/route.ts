import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function POST(request: NextRequest) {
  try {
    const { message, context } = await request.json()
    
    if (!message) {
      return NextResponse.json(
        { error: '缺少消息内容' },
        { status: 400 }
      )
    }
    
    // 构建上下文
    const chatPrompt = process.env.AI_CHAT_PROMPT || '基于以下原文内容和AI笔记，请回答用户的问题。保持回答准确、有用且基于提供的内容。'
    
    const contextContent = `
原文内容：
${context?.originalContent || '无原文内容'}

AI笔记：
${context?.aiNote || '无AI笔记'}

用户问题：${message}
`
    
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4',
      messages: [
        {
          role: 'system',
          content: chatPrompt
        },
        {
          role: 'user',
          content: contextContent
        }
      ],
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
    })
    
    const aiResponse = response.choices[0]?.message?.content || '抱歉，我无法回答这个问题。'
    
    return NextResponse.json({
      response: aiResponse,
      success: true
    })
    
  } catch (error) {
    console.error('聊天请求失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '聊天失败',
        success: false 
      },
      { status: 500 }
    )
  }
}
