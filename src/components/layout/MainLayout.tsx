'use client'

import React, { useState, useRef, useCallback } from 'react'
import { useAppStore } from '@/lib/store'
import WorkArea from './WorkArea'
import AIAssistant from './AIAssistant'

const MainLayout: React.FC = () => {
  const { tabs } = useAppStore()
  const [leftPanelWidth, setLeftPanelWidth] = useState(70) // 默认70%
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // 处理拖拽开始
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  // 处理拖拽过程
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const newLeftWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100

    // 限制宽度范围：30% - 80%
    const clampedWidth = Math.max(30, Math.min(80, newLeftWidth))
    setLeftPanelWidth(clampedWidth)
  }, [isDragging])

  // 处理拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // 添加全局鼠标事件监听
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* 顶部导航栏 */}
      <header className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-semibold text-gray-900">沉淀</h1>
        </div>
        <div className="text-sm text-gray-500">
          {tabs.length > 0 && `${tabs.length} 个标签页`}
        </div>
      </header>

      {/* 主要内容区域 */}
      <div
        ref={containerRef}
        className="flex-1 flex overflow-hidden"
      >
        {/* 主工作区 */}
        <div
          className="flex flex-col"
          style={{
            width: tabs.length > 0 ? `${leftPanelWidth}%` : '100%',
            transition: isDragging ? 'none' : 'width 0.2s ease'
          }}
        >
          <WorkArea />
        </div>

        {/* 可拖拽分隔条 - 只在有标签页时显示 */}
        {tabs.length > 0 && (
          <div
            className={`w-1 bg-gray-200 hover:bg-gray-300 cursor-col-resize transition-colors ${
              isDragging ? 'bg-blue-400' : ''
            }`}
            onMouseDown={handleMouseDown}
            style={{
              minWidth: '4px',
              maxWidth: '4px'
            }}
          >
            <div className="w-full h-full flex items-center justify-center">
              <div className="w-0.5 h-8 bg-gray-400 rounded-full"></div>
            </div>
          </div>
        )}

        {/* AI助手面板 - 只在有标签页时显示 */}
        {tabs.length > 0 && (
          <div
            className="bg-white border-l border-gray-200"
            style={{
              width: `${100 - leftPanelWidth}%`,
              transition: isDragging ? 'none' : 'width 0.2s ease'
            }}
          >
            <AIAssistant />
          </div>
        )}
      </div>
    </div>
  )
}

export default MainLayout
