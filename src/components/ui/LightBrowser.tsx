'use client'

import React, { useState, useRef, useEffect } from 'react'
import { RefreshCw, ExternalLink, AlertCircle } from 'lucide-react'

interface LightBrowserProps {
  url: string
  title: string
  onLoadComplete?: () => void
  onError?: (error: string) => void
}

const LightBrowser: React.FC<LightBrowserProps> = ({ 
  url, 
  title, 
  onLoadComplete, 
  onError 
}) => {
  const [loading, setLoading] = useState(false) // 改为false，立即显示iframe
  const [error, setError] = useState<string | null>(null)
  const [currentUrl, setCurrentUrl] = useState(url)
  const [loadStartTime, setLoadStartTime] = useState<number>(0)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // 处理iframe加载开始
  const handleLoadStart = () => {
    setLoadStartTime(Date.now())
    setLoading(true)
    setError(null)
  }

  // 处理iframe加载完成
  const handleLoad = () => {
    const loadTime = Date.now() - loadStartTime
    console.log(`页面加载完成，耗时: ${loadTime}ms`)
    setLoading(false)
    setError(null)

    // 尝试注入导航脚本
    setTimeout(() => {
      injectNavigationScript()
    }, 100)

    onLoadComplete?.()
  }

  // 处理iframe加载错误
  const handleError = () => {
    setLoading(false)
    const errorMsg = '无法加载网页内容'
    setError(errorMsg)
    onError?.(errorMsg)
  }

  // 刷新页面
  const handleRefresh = () => {
    if (iframeRef.current) {
      handleLoadStart()
      iframeRef.current.src = currentUrl
    }
  }

  // 在新窗口打开
  const handleOpenExternal = () => {
    window.open(currentUrl, '_blank')
  }

  // 检查URL是否可以在iframe中显示
  const isIframeCompatible = (url: string) => {
    try {
      const urlObj = new URL(url)
      // 一些网站不允许在iframe中显示
      const blockedDomains = ['youtube.com', 'facebook.com', 'twitter.com', 'instagram.com']
      return !blockedDomains.some(domain => urlObj.hostname.includes(domain))
    } catch {
      return false
    }
  }

  useEffect(() => {
    if (url !== currentUrl) {
      setCurrentUrl(url)
      handleLoadStart()
    }
  }, [url, currentUrl])

  // 监听iframe内的链接点击事件
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 只处理来自iframe的消息
      if (event.source === iframeRef.current?.contentWindow) {
        if (event.data.type === 'navigation') {
          const newUrl = event.data.url
          if (newUrl && newUrl !== currentUrl) {
            // 在当前标签页中导航到新URL
            setCurrentUrl(newUrl)
            handleLoadStart()
          }
        }
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [currentUrl])

  // 注入脚本到iframe中监听链接点击
  const injectNavigationScript = () => {
    if (iframeRef.current?.contentWindow) {
      try {
        const script = `
          (function() {
            // 监听所有链接点击
            document.addEventListener('click', function(e) {
              const link = e.target.closest('a');
              if (link && link.href) {
                e.preventDefault();
                // 发送消息给父窗口
                window.parent.postMessage({
                  type: 'navigation',
                  url: link.href
                }, '*');
              }
            });
          })();
        `

        const iframe = iframeRef.current
        const doc = iframe.contentDocument || iframe.contentWindow?.document
        if (doc) {
          const scriptElement = doc.createElement('script')
          scriptElement.textContent = script
          doc.head.appendChild(scriptElement)
        }
      } catch (error) {
        // 跨域限制，无法注入脚本
        console.log('无法注入导航脚本，可能存在跨域限制')
      }
    }
  }

  // 如果URL不兼容iframe，显示替代内容
  if (!isIframeCompatible(currentUrl)) {
    return (
      <div className="h-full flex flex-col">
        {/* 浏览器工具栏 */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2 flex-1">
            <div className="flex items-center space-x-1 text-sm text-gray-600">
              <span className="truncate max-w-md">{currentUrl}</span>
            </div>
          </div>
          <button
            onClick={handleOpenExternal}
            className="p-1 hover:bg-gray-200 rounded transition-colors"
            title="在新窗口打开"
          >
            <ExternalLink size={16} />
          </button>
        </div>

        {/* 不兼容提示 */}
        <div className="flex-1 flex items-center justify-center bg-gray-50">
          <div className="text-center p-8">
            <AlertCircle size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">无法在内嵌浏览器中显示</h3>
            <p className="text-gray-600 mb-4">
              此网站不允许在框架中显示内容
            </p>
            <button
              onClick={handleOpenExternal}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ExternalLink size={16} />
              <span>在新窗口打开</span>
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 浏览器工具栏 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2 flex-1">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-1 hover:bg-gray-200 rounded transition-colors disabled:opacity-50"
            title="刷新"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
          </button>
          <div className="flex items-center space-x-1 text-sm text-gray-600">
            <span className="truncate max-w-md">{currentUrl}</span>
          </div>
        </div>
        <button
          onClick={handleOpenExternal}
          className="p-1 hover:bg-gray-200 rounded transition-colors"
          title="在新窗口打开"
        >
          <ExternalLink size={16} />
        </button>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">正在加载网页...</p>
            <p className="text-xs text-gray-500 mt-1">AI分析将在后台进行</p>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="flex-1 flex items-center justify-center bg-gray-50">
          <div className="text-center p-8">
            <AlertCircle size={48} className="mx-auto text-red-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="space-x-2">
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                重试
              </button>
              <button
                onClick={handleOpenExternal}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                在新窗口打开
              </button>
            </div>
          </div>
        </div>
      )}

      {/* iframe内容 */}
      {!error && (
        <div className="flex-1 relative">
          <iframe
            ref={iframeRef}
            src={currentUrl}
            className="w-full h-full border-0"
            onLoad={handleLoad}
            onError={handleError}
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox allow-top-navigation"
            title={title}
            loading="eager"
            style={{
              backgroundColor: '#ffffff',
              transition: 'opacity 0.3s ease-in-out',
              opacity: loading ? 0.7 : 1
            }}
          />
        </div>
      )}
    </div>
  )
}

export default LightBrowser
