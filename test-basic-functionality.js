// 基本功能测试脚本
// 运行: node test-basic-functionality.js

const testAPI = async () => {
  const baseUrl = 'http://localhost:3000'
  
  console.log('🧪 开始测试基本功能...\n')
  
  // 测试1: 处理文本内容
  console.log('📝 测试1: 处理文本内容')
  try {
    const response = await fetch(`${baseUrl}/api/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: '这是一段测试文本。人工智能正在改变我们的世界，它带来了前所未有的机遇和挑战。我们需要学会与AI协作，而不是被它取代。',
        type: 'text'
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 文本处理成功')
      console.log('标题:', data.title)
      console.log('AI笔记长度:', data.aiNote?.length || 0, '字符')
    } else {
      console.log('❌ 文本处理失败:', response.status)
    }
  } catch (error) {
    console.log('❌ 文本处理错误:', error.message)
  }
  
  console.log('\n' + '='.repeat(50) + '\n')
  
  // 测试2: 获取会话列表
  console.log('📋 测试2: 获取会话列表')
  try {
    const response = await fetch(`${baseUrl}/api/archive/list`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 获取会话列表成功')
      console.log('会话数量:', data.sessions?.length || 0)
    } else {
      console.log('❌ 获取会话列表失败:', response.status)
    }
  } catch (error) {
    console.log('❌ 获取会话列表错误:', error.message)
  }
  
  console.log('\n' + '='.repeat(50) + '\n')
  
  // 测试3: 保存会话
  console.log('💾 测试3: 保存会话')
  try {
    const response = await fetch(`${baseUrl}/api/archive/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: '测试会话',
        sourceType: 'text',
        sourceData: '测试数据',
        originalContent: '<p>这是测试内容</p>',
        aiNoteMarkdown: '# 测试AI笔记\n\n这是一个测试笔记。'
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 保存会话成功')
      console.log('会话ID:', data.id)
    } else {
      console.log('❌ 保存会话失败:', response.status)
    }
  } catch (error) {
    console.log('❌ 保存会话错误:', error.message)
  }
  
  console.log('\n🎉 测试完成!')
}

// 运行测试
testAPI().catch(console.error)
