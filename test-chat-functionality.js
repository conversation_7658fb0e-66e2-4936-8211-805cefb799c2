// 测试聊天功能
const testChat = async () => {
  const baseUrl = 'http://localhost:3000'
  
  console.log('💬 测试AI聊天功能...\n')
  
  try {
    const response = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: '这篇文章的主要观点是什么？',
        context: {
          originalContent: '人工智能正在改变我们的世界，它带来了前所未有的机遇和挑战。我们需要学会与AI协作，而不是被它取代。',
          aiNote: '# AI与人类协作\n\n## 核心观点\n- AI正在改变世界\n- 带来机遇和挑战\n- 需要学会协作'
        }
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 聊天功能正常')
      console.log('AI回复:', data.response)
    } else {
      console.log('❌ 聊天功能失败:', response.status)
    }
  } catch (error) {
    console.log('❌ 聊天功能错误:', error.message)
  }
  
  console.log('\n🎉 聊天测试完成!')
}

testChat().catch(console.error)
